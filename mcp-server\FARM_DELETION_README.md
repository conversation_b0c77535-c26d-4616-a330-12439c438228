# Farm Deletion Functionality

This document describes the farm deletion functionality implemented in the MCP server chat interface.

## Overview

The farm deletion feature allows users to delete farms through natural language commands in both English and Urdu. The system provides two modes of operation:

1. **Direct deletion** - When a specific farm name is mentioned
2. **Selection-based deletion** - When no farm name is provided, showing a list for selection

## Supported Commands

### English Commands
- `Delete [Farm Name]`
- `Remove [Farm Name]` 
- `Demolish [Farm Name]`
- `Delete the farm`
- `Remove the farm`
- `Demolish the farm`

### Urdu Commands
- `[Farm Name] کو حذف کریں`
- `فارم کو ہٹا دیں`
- `فارم کو منہدم کریں`
- `حذف کریں [Farm Name]`

## Usage Examples

### Direct Farm Deletion
```
User: "Delete Haven View Farm"
AI: "✅ Farm Successfully Deleted:

Name: Haven View Farm
🆔 Database ID: farm-123

✅ All related data (animals, expenses, milking records, etc.) has also been deleted."
```

### Farm Selection Interface
```
User: "Delete the farm"
AI: "🗑️ Delete Farm

Please select the farm you want to delete:

1. Haven View Farm (Rural Road 123)
2. Green Valley Farm (Valley Street 456)
3. Sunny Acres (Sunny Lane 789)

⚠️ Warning: Deleting a farm will also delete all related data (animals, expenses, milking records, etc.)."
```

### Urdu Interface
```
User: "فارم کو حذف کریں"
AI: "🗑️ فارم حذف کرنا

براہ کرم اس فارم کا انتخاب کریں جسے آپ حذف کرنا چاہتے ہیں:

1. Haven View Farm (Rural Road 123)
2. Green Valley Farm (Valley Street 456)
3. Sunny Acres (Sunny Lane 789)

⚠️ انتباہ: فارم حذف کرنے سے تمام متعلقہ ڈیٹا بھی حذف ہو جائے گا۔"
```

## API Request Format

### Direct Deletion Request
```json
{
  "prompt": "Delete Haven View Farm",
  "userId": "user-123",
  "language": "en",
  "farms": [
    {
      "id": "farm-1",
      "name": "Haven View Farm",
      "location": "Rural Road 123"
    }
  ]
}
```

### Farm Selection Response
```json
{
  "message": "Please select the farm you want to delete...",
  "needsFarmSelection": true,
  "selectionType": "farm_deletion",
  "farmList": [
    {
      "id": "farm-1",
      "label": "Haven View Farm",
      "description": "Rural Road 123",
      "imageUri": null
    }
  ],
  "context": {
    "action": "delete_farm",
    "needsFarmSelection": true
  }
}
```

### Farm Selection Request
```json
{
  "prompt": "farm-1",
  "userId": "user-123",
  "language": "en",
  "farms": [...],
  "selectedFarmId": "farm-1",
  "context": {
    "action": "delete_farm",
    "needsFarmSelection": true
  }
}
```

## Security & Permissions

- Only farm owners can delete their farms
- Permission is verified using the `ownerId` field in the farm document
- Unauthorized deletion attempts return permission denied errors

## Data Cleanup

When a farm is deleted, the system performs comprehensive cleanup:

1. **Subcollections**: All subcollections are deleted including:
   - animals
   - expenses
   - milking records
   - health checks
   - tasks
   - activities
   - pregnancies

2. **User References**: The farm ID is removed from:
   - Owner's `assignedFarmIds` array
   - All users' `assignedFarmIds` arrays who had access

3. **Farm Document**: Finally, the main farm document is deleted

## Error Handling

### Farm Not Found
```json
{
  "message": "❌ Farm \"Unknown Farm\" not found.\n\nAvailable farms:\n• Haven View Farm\n• Green Valley Farm\n\nPlease use the correct name or select from the list.",
  "error": true,
  "availableFarms": [...]
}
```

### Permission Denied
```json
{
  "message": "❌ Error deleting farm: Permission denied: Only farm owner can delete the farm",
  "error": true
}
```

### No Farms Available
```json
{
  "message": "❌ No farms available.",
  "error": true
}
```

## Frontend Integration

The frontend should handle the farm deletion responses by:

1. **Direct Deletion**: Show success message and refresh farm list
2. **Selection Interface**: Display farm selection dropdown/list
3. **Error Handling**: Show appropriate error messages
4. **Confirmation**: Consider adding confirmation dialogs for destructive actions

## Testing

Use the provided test script to verify functionality:

```bash
node mcp-server/test-farm-deletion.js
```

The test script covers:
- Direct farm deletion with specific name
- Farm selection interface when no name provided
- Farm selection and deletion flow
- Urdu language support
