const axios = require('axios');

// Test configuration
const SERVER_URL = 'http://localhost:3001';
const TEST_USER_ID = 'test-user-123';

// Test data - simulating farms from dashboard
const testFarms = [
  {
    id: 'farm-1',
    name: 'Haven View Farm',
    location: 'Rural Road 123'
  },
  {
    id: 'farm-2', 
    name: 'Green Valley Farm',
    location: 'Valley Street 456'
  },
  {
    id: 'farm-3',
    name: 'Sunny Acres',
    location: 'Sunny Lane 789'
  }
];

// Test farm deletion with specific farm name
async function testFarmDeletionWithName() {
  console.log('🎯 Testing Farm Deletion with Specific Name...\n');

  const testRequest = {
    prompt: 'Delete Haven View Farm',
    userId: TEST_USER_ID,
    language: 'en',
    farms: testFarms
  };

  try {
    console.log('📤 Sending request:', JSON.stringify(testRequest, null, 2));
    
    const response = await axios.post(`${SERVER_URL}/open-ai-chat`, testRequest);
    
    console.log('📥 Response status:', response.status);
    console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.deleted) {
      console.log('✅ Farm deletion successful!');
    } else if (response.data.error) {
      console.log('❌ Farm deletion failed:', response.data.message);
    } else {
      console.log('⚠️ Unexpected response format');
    }
  } catch (error) {
    console.error('❌ Request failed:', error.response?.data || error.message);
  }
}

// Test farm deletion without specific name (should show list)
async function testFarmDeletionWithoutName() {
  console.log('\n🎯 Testing Farm Deletion without Specific Name...\n');

  const testRequest = {
    prompt: 'Delete the farm',
    userId: TEST_USER_ID,
    language: 'en',
    farms: testFarms
  };

  try {
    console.log('📤 Sending request:', JSON.stringify(testRequest, null, 2));
    
    const response = await axios.post(`${SERVER_URL}/open-ai-chat`, testRequest);
    
    console.log('📥 Response status:', response.status);
    console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.needsFarmSelection) {
      console.log('✅ Farm selection interface displayed!');
      console.log('📋 Available farms:', response.data.farmList?.length || 0);
    } else if (response.data.error) {
      console.log('❌ Request failed:', response.data.message);
    } else {
      console.log('⚠️ Unexpected response format');
    }
  } catch (error) {
    console.error('❌ Request failed:', error.response?.data || error.message);
  }
}

// Test farm selection for deletion
async function testFarmSelectionForDeletion() {
  console.log('\n🎯 Testing Farm Selection for Deletion...\n');

  const testRequest = {
    prompt: 'farm-2', // Selecting Green Valley Farm
    userId: TEST_USER_ID,
    language: 'en',
    farms: testFarms,
    selectedFarmId: 'farm-2',
    context: {
      action: 'delete_farm',
      needsFarmSelection: true
    }
  };

  try {
    console.log('📤 Sending request:', JSON.stringify(testRequest, null, 2));
    
    const response = await axios.post(`${SERVER_URL}/open-ai-chat`, testRequest);
    
    console.log('📥 Response status:', response.status);
    console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.deleted) {
      console.log('✅ Farm deletion from selection successful!');
    } else if (response.data.error) {
      console.log('❌ Farm deletion failed:', response.data.message);
    } else {
      console.log('⚠️ Unexpected response format');
    }
  } catch (error) {
    console.error('❌ Request failed:', error.response?.data || error.message);
  }
}

// Test Urdu farm deletion
async function testUrduFarmDeletion() {
  console.log('\n🎯 Testing Urdu Farm Deletion...\n');

  const testRequest = {
    prompt: 'Haven View Farm کو حذف کریں',
    userId: TEST_USER_ID,
    language: 'ur',
    farms: testFarms
  };

  try {
    console.log('📤 Sending request:', JSON.stringify(testRequest, null, 2));
    
    const response = await axios.post(`${SERVER_URL}/open-ai-chat`, testRequest);
    
    console.log('📥 Response status:', response.status);
    console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.deleted) {
      console.log('✅ Urdu farm deletion successful!');
    } else if (response.data.needsFarmSelection) {
      console.log('✅ Urdu farm selection interface displayed!');
    } else if (response.data.error) {
      console.log('❌ Urdu farm deletion failed:', response.data.message);
    } else {
      console.log('⚠️ Unexpected response format');
    }
  } catch (error) {
    console.error('❌ Request failed:', error.response?.data || error.message);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Farm Deletion Tests...\n');
  
  await testFarmDeletionWithName();
  await testFarmDeletionWithoutName();
  await testFarmSelectionForDeletion();
  await testUrduFarmDeletion();
  
  console.log('\n✅ All tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testFarmDeletionWithName,
  testFarmDeletionWithoutName,
  testFarmSelectionForDeletion,
  testUrduFarmDeletion,
  runAllTests
};
